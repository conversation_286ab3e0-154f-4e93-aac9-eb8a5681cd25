/**
 * Modern Effects and Animations Library
 * Enhanced UI/UX for admin-identity-almkhtar project
 * Compatible with Bootstrap 5.3 and jQuery 3.6
 */

$(document).ready(function() {
    'use strict';

    // Initialize all modern effects
    initializeModernEffects();

    function initializeModernEffects() {
        initializeAOS();
        initializeParticles();
        initializeTypingEffect();
        initializeCounterAnimation();
        initializeProgressBars();
        initializeTooltipEnhancements();
        initializeModalEnhancements();
        initializeButtonEffects();
        initializeFormEnhancements();
        initializeTableEnhancements();
        initializeScrollEffects();
    }

    // Initialize AOS (Animate On Scroll)
    function initializeAOS() {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out-cubic',
                once: true,
                offset: 50,
                delay: 100,
                anchorPlacement: 'top-bottom'
            });

            // Refresh AOS on dynamic content load
            $(document).on('contentLoaded', function() {
                AOS.refresh();
            });
        }
    }

    // Particle background effect
    function initializeParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 50, density: { enable: true, value_area: 800 } },
                    color: { value: '#ffffff' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.1, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#ffffff',
                        opacity: 0.1,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 2,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }
    }

    // Typing effect for headers
    function initializeTypingEffect() {
        $('.typing-effect').each(function() {
            const $element = $(this);
            const text = $element.text();
            $element.text('');
            
            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    $element.text($element.text() + text.charAt(i));
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };
            
            // Start typing when element is in view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        typeWriter();
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe($element[0]);
        });
    }

    // Counter animation for statistics
    function initializeCounterAnimation() {
        $('.counter').each(function() {
            const $counter = $(this);
            const target = parseInt($counter.text());
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounter($counter, target);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe($counter[0]);
        });
    }

    function animateCounter($element, target) {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            $element.text(Math.floor(current));
        }, 20);
    }

    // Progress bar animations
    function initializeProgressBars() {
        $('.progress-bar-animated').each(function() {
            const $bar = $(this);
            const width = $bar.data('width') || $bar.attr('aria-valuenow');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        $bar.css('width', '0%').animate({ width: width + '%' }, 1500);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe($bar[0]);
        });
    }

    // Enhanced tooltips
    function initializeTooltipEnhancements() {
        // Custom tooltip styles
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                animation: true,
                delay: { show: 300, hide: 100 },
                html: true,
                placement: 'auto'
            });
        });
    }

    // Modal enhancements
    function initializeModalEnhancements() {
        // Add backdrop blur effect
        $('.modal').on('show.bs.modal', function() {
            $('body').addClass('modal-open-blur');
            $(this).find('.modal-dialog').addClass('modal-slide-in');
        });

        $('.modal').on('hidden.bs.modal', function() {
            $('body').removeClass('modal-open-blur');
            $(this).find('.modal-dialog').removeClass('modal-slide-in');
        });

        // Add shake animation for validation errors
        $('.modal').on('shake', function() {
            $(this).find('.modal-dialog').addClass('shake-animation');
            setTimeout(() => {
                $(this).find('.modal-dialog').removeClass('shake-animation');
            }, 500);
        });
    }

    // Button effects
    function initializeButtonEffects() {
        // Ripple effect for buttons
        $('.btn').on('click', function(e) {
            const $button = $(this);
            const $ripple = $('<span class="ripple"></span>');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            $ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });
            
            $button.append($ripple);
            
            setTimeout(() => {
                $ripple.remove();
            }, 600);
        });

        // Pulse effect for important buttons
        $('.btn-pulse').each(function() {
            setInterval(() => {
                $(this).addClass('pulse-animation');
                setTimeout(() => {
                    $(this).removeClass('pulse-animation');
                }, 1000);
            }, 3000);
        });
    }

    // Form enhancements
    function initializeFormEnhancements() {
        // Floating labels animation
        $('.form-floating input, .form-floating select, .form-floating textarea').on('focus blur', function() {
            const $input = $(this);
            const $label = $input.siblings('label');
            
            if ($input.val() || $input.is(':focus')) {
                $label.addClass('active');
            } else {
                $label.removeClass('active');
            }
        });

        // Input validation animations
        $('.form-control').on('invalid', function() {
            $(this).addClass('shake-input');
            setTimeout(() => {
                $(this).removeClass('shake-input');
            }, 500);
        });

        // Success animation for valid inputs
        $('.form-control').on('input', function() {
            const $input = $(this);
            if ($input[0].checkValidity()) {
                $input.addClass('success-glow');
                setTimeout(() => {
                    $input.removeClass('success-glow');
                }, 1000);
            }
        });
    }

    // Table enhancements
    function initializeTableEnhancements() {
        // Row hover effects
        $('.table tbody tr').hover(
            function() {
                $(this).addClass('table-row-hover');
            },
            function() {
                $(this).removeClass('table-row-hover');
            }
        );

        // Sortable columns animation
        $('.table th[data-sort]').on('click', function() {
            $(this).addClass('sort-animation');
            setTimeout(() => {
                $(this).removeClass('sort-animation');
            }, 300);
        });
    }

    // Scroll effects
    function initializeScrollEffects() {
        // Parallax effect for background
        $(window).on('scroll', function() {
            const scrolled = $(this).scrollTop();
            const parallax = $('.parallax-bg');
            const speed = 0.5;
            
            parallax.css('transform', `translateY(${scrolled * speed}px)`);
        });

        // Scroll to top button
        const $scrollTop = $('<button class="scroll-to-top btn btn-primary rounded-circle"><i class="fas fa-arrow-up"></i></button>');
        $('body').append($scrollTop);

        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $scrollTop.addClass('show');
            } else {
                $scrollTop.removeClass('show');
            }
        });

        $scrollTop.on('click', function() {
            $('html, body').animate({ scrollTop: 0 }, 800);
        });

        // Header shrink on scroll
        $(window).on('scroll', function() {
            const $header = $('.header');
            if ($(this).scrollTop() > 100) {
                $header.addClass('header-shrink');
            } else {
                $header.removeClass('header-shrink');
            }
        });
    }

    // Custom SweetAlert2 configurations
    window.showSuccessAlert = function(title, text) {
        return Swal.fire({
            title: title,
            text: text,
            icon: 'success',
            confirmButtonColor: '#27ae60',
            showClass: {
                popup: 'animate__animated animate__fadeInDown'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOutUp'
            }
        });
    };

    window.showErrorAlert = function(title, text) {
        return Swal.fire({
            title: title,
            text: text,
            icon: 'error',
            confirmButtonColor: '#e74c3c',
            showClass: {
                popup: 'animate__animated animate__shakeX'
            }
        });
    };

    window.showConfirmAlert = function(title, text, confirmText = 'نعم', cancelText = 'إلغاء') {
        return Swal.fire({
            title: title,
            text: text,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3498db',
            cancelButtonColor: '#95a5a6',
            confirmButtonText: confirmText,
            cancelButtonText: cancelText,
            reverseButtons: true
        });
    };

    // Loading overlay
    window.showLoadingOverlay = function(text = 'جاري التحميل...') {
        Swal.fire({
            title: text,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    };

    window.hideLoadingOverlay = function() {
        Swal.close();
    };
});

// CSS animations (to be added to style.css)
const modernEffectsCSS = `
/* Modern Effects CSS */
.modal-open-blur .container-fluid {
    filter: blur(3px);
    transition: filter 0.3s ease;
}

.modal-slide-in {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.shake-animation {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.pulse-animation {
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.shake-input {
    animation: shakeInput 0.5s ease-in-out;
}

@keyframes shakeInput {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

.success-glow {
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
    transition: box-shadow 0.3s ease;
}

.table-row-hover {
    background-color: rgba(52, 152, 219, 0.1) !important;
    transform: translateX(5px);
    transition: all 0.3s ease;
}

.sort-animation {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.header-shrink {
    transform: scale(0.95);
    transition: transform 0.3s ease;
}
`;

// Inject CSS if not already present
if (!document.getElementById('modern-effects-css')) {
    const style = document.createElement('style');
    style.id = 'modern-effects-css';
    style.textContent = modernEffectsCSS;
    document.head.appendChild(style);
}
