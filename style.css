/*
* Modern CSS Variables and Imports for Bootstrap 5.3 Compatibility
*/
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url(//fonts.googleapis.com/earlyaccess/notokufiarabic.css);

@font-face {
  font-family: 'Al Hurra Txt Bold';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot);
  src: url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.eot?#iefix)
      format('embedded-opentype'),
    url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.woff2) format('woff2'),
    url(../fonts/Al-Hurra/AlHurraTxtBold.woff) format('woff'),
    url(../fonts/Al-Hurra/Al-Hurra-Txtreg-Bold.ttf) format('truetype');
}

/* CSS Variables for Modern Design System */
:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --light-bg: #ecf0f1;
  --dark-bg: #34495e;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --text-muted: #6c757d;
  --border-color: #dee2e6;
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --border-radius-sm: 8px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --box-shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.12);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-in-out;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  background: var(--gradient-primary);
  background-attachment: fixed;
  font-family: 'Cairo', 'Noto Kufi Arabic', sans-serif !important;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-dark);
  -webkit-print-color-adjust: exact;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  min-height: 100vh;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url(bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.1;
  z-index: -1;
}
/* Modern Component Styles */
canvas {
    position: absolute;
}

#appendCanvas {
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    direction: ltr;
}

/* Enhanced Table Styles */
.table {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    overflow: hidden;
}

.table tr td,
.table tr th {
    vertical-align: middle !important;
    border-color: var(--border-color);
    padding: 1rem 0.75rem;
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    border: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    transform: translateY(-1px);
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-success {
    background: var(--gradient-success);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

/* Enhanced Form Styles */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background: rgba(255, 255, 255, 1);
}

/* Modern File Upload */
.file-upload {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    width: 280px;
    margin: 0 auto;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 2px dashed var(--border-color);
    transition: var(--transition);
}
.file-upload:hover {
    border-color: var(--secondary-color);
    background: rgba(255, 255, 255, 1);
}

.file-upload-btn {
    width: 100%;
    margin: 0;
    color: #fff;
    background: var(--gradient-success);
    border: none;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    outline: none;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.file-upload-btn:hover {
    background: var(--gradient-success);
    color: #ffffff;
    transition: var(--transition);
    cursor: pointer;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.file-upload-btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.file-upload-content {
  display: none;
  text-align: center;
}

.file-upload-input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}

.image-upload-wrap {
  margin-top: 20px;
  border: 4px dashed #1fb264;
  position: relative;
}

.image-dropping,
.image-upload-wrap:hover {
  background-color: #1fb264;
  border: 4px dashed #ffffff;
}

.image-title-wrap {
  padding: 0 15px 15px 15px;
  color: #222;
}

.drag-text {
  text-align: center;
}

.drag-text h3 {
  font-weight: 100;
  text-transform: uppercase;
  color: #15824b;
  padding: 60px 0;
}

.file-upload-image {
  max-height: 200px;
  max-width: 200px;
  margin: auto;
  padding: 20px;
}

.remove-image {
  width: 200px;
  margin: 0;
  color: #fff;
  background: #6e72ea;
  border: none;
  padding: 10px;
  border-radius: 4px;
  border-bottom: 4px solid #1f23ab;
  transition: all 0.2s ease;
  outline: none;
}

.remove-image:hover {
  background: #c13b2a;
  color: #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.remove-image:active {
  border: 0;
  transition: all 0.2s ease;
}
@media (max-width: 767px) {
  .file-upload {
    position: inherit !important;
  }
}
/* Modern Login Styles */
.login {
    max-width: 480px;
    margin: 2rem auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.img-login img {
    max-width: 400px;
    filter: drop-shadow(0 5px 15px rgba(0,0,0,0.1));
}

/* Enhanced Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: none;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--secondary-color);
}

.header h4 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header h6 {
    color: var(--text-light);
    font-weight: 500;
    margin-bottom: 0.25rem;
}
.page-item {
  font-size: 12px;
}
.hint-text {
  font-size: 13px;
  padding-bottom: 8px;
  padding-top: 8px;
}
.header {
  border-bottom: 2px solid #777;
  padding: 11px;
  margin-bottom: 15px;
}
.header h4:first-child {
  margin-top: -4px;
  line-height: 3;
}
.customDiv {
  text-align: center;
  margin-top: 50px;
  border: 2px solid;
  padding: 86px;
}
.customDiv:first-child {
  margin-top: 0 !important;
  border: none !important;
}
#showErrors li:last-child {
  display: none;
}
.inputSize .form-control {
  padding: 5px !important;
}

/* Enhanced Search Section */
.advancedSearch {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 2px solid var(--border-color);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    position: relative;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    transition: var(--transition);
}

.advancedSearch:hover {
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.spanSearch {
    position: absolute;
    top: -17px;
    right: 29px;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Enhanced Table Styles Override */
.table td,
.table th {
    padding: 0.75rem !important;
    border-color: var(--border-color);
}

.table thead th {
    font-size: 0.9rem !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Modern Modal Styles */
.modal-content {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border: none;
    padding: 1.5rem;
    background: rgba(248, 249, 250, 0.5);
}

/* Enhanced Pagination */
.pagination {
    margin: 2rem 0;
}

.page-link {
    border: none;
    border-radius: var(--border-radius-sm);
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.page-link:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.page-item.active .page-link {
    background: var(--gradient-primary);
    border: none;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Responsive Design */
/* Extra Small devices (phones, 576px and down) */
@media (max-width: 575.98px) {
    .container-fluid {
        width: 100% !important;
        padding: 0.5rem !important;
    }

    .header {
        padding: 1rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .header h2 {
        font-size: 1.25rem;
    }

    .header h4 {
        font-size: 1rem;
    }

    .breadcrumb-custom {
        font-size: 0.8rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .breadcrumb-separator {
        display: none;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    .advancedSearch {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .spanSearch {
        font-size: 0.8rem;
        padding: 0.25rem 0.75rem;
        right: 15px;
        top: -12px;
    }

    .login {
        margin: 0.5rem;
        padding: 1.5rem;
    }

    .floating-actions {
        left: 10px !important;
        transform: translateY(-50%) scale(0.8) !important;
    }

    .floating-actions .btn {
        width: 45px !important;
        height: 45px !important;
        font-size: 0.875rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .stats-card .card-body {
        padding: 1rem;
    }

    .stats-card h3 {
        font-size: 1.5rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        margin: 0 -0.5rem;
    }

    .table {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem !important;
        white-space: nowrap;
    }

    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .modal-body {
        padding: 1rem;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        font-size: 0.9rem;
        padding: 0.75rem;
    }

    .form-floating > label {
        font-size: 0.85rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container-fluid {
        width: 95% !important;
        padding: 1rem !important;
    }

    .header {
        padding: 1.25rem;
        text-align: center;
    }

    .header h2 {
        font-size: 1.5rem;
    }

    .advancedSearch {
        padding: 1.5rem;
    }

    .floating-actions {
        left: 15px !important;
        transform: translateY(-50%) scale(0.9) !important;
    }

    .floating-actions .btn {
        width: 50px !important;
        height: 50px !important;
    }

    .stats-card h3 {
        font-size: 1.75rem;
    }

    .table {
        font-size: 0.9rem;
    }

    .modal-dialog {
        max-width: 90%;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container-fluid {
        width: 90% !important;
    }

    .header {
        padding: 1.5rem;
    }

    .floating-actions {
        left: 20px !important;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .modal-dialog {
        max-width: 80%;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .container-fluid {
        width: 85% !important;
    }

    .modal-dialog {
        max-width: 70%;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container-fluid {
        width: 80% !important;
        max-width: 1400px;
    }

    .modal-dialog {
        max-width: 60%;
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .header {
        padding: 0.75rem;
    }

    .header h2 {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .header h4 {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .breadcrumb-custom {
        font-size: 0.8rem;
        margin-bottom: 0.5rem;
    }

    .stats-card .card-body {
        padding: 0.75rem;
    }

    .stats-card h3 {
        font-size: 1.25rem;
    }

    .stats-card h5 {
        font-size: 0.9rem;
    }

    .advancedSearch {
        padding: 1rem;
    }

    .floating-actions {
        top: 40% !important;
    }

    .floating-actions .btn {
        width: 45px !important;
        height: 45px !important;
        margin-bottom: 0.5rem;
    }
}

/* Print styles */
@media print {
    .floating-actions,
    .modal,
    .btn,
    .action-buttons {
        display: none !important;
    }

    .header {
        background: white !important;
        color: black !important;
        border-bottom: 2px solid #000;
    }

    .table {
        background: white !important;
        color: black !important;
    }

    .table thead th {
        background: #f8f9fa !important;
        color: black !important;
        border: 1px solid #000 !important;
    }

    .table tbody td {
        border: 1px solid #000 !important;
    }

    .advancedSearch {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --secondary-color: #0066cc;
        --text-dark: #000000;
        --text-light: #333333;
        --border-color: #000000;
        --box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }

    .btn {
        border: 2px solid currentColor;
    }

    .form-control,
    .form-select {
        border: 2px solid #000000;
    }

    .table {
        border: 2px solid #000000;
    }

    .table th,
    .table td {
        border: 1px solid #000000;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-actions .btn:hover {
        transform: none !important;
    }

    .btn:hover {
        transform: none !important;
    }

    .card:hover {
        transform: none !important;
    }
}
