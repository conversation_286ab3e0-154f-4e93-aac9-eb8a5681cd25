<?php
ob_start();
session_start();
date_default_timezone_set("Asia/Baghdad");
if (isset($_SESSION['LoginToAdminIdentity'])) {
    require '../init.php';



?>
    <!doctype html>
    <html lang="ar" dir="rtl">

    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <!-- Bootstrap 5.3 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <!-- AOS Animation -->
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
        <!-- SweetAlert2 -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
        <!-- Custom CSS -->
        <link rel="stylesheet" href="style.css">
        <title>نافذة التقديم على هوية المختار - ديوان محافظة كركوك</title>

        <!-- Favicon -->
        <link rel="icon" type="image/gif" href="logo.gif">
    </head>

    <body>
        <!-- Loading Screen -->
        <div id="loadingScreen" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); z-index: 9999;">
            <div class="text-center text-white">
                <div class="spinner mb-3"></div>
                <h5>جاري تحميل النظام...</h5>
            </div>
        </div>

        <!-- Floating Action Buttons -->
        <div class="floating-actions" style="position: fixed; left: 20px; top: 50%; transform: translateY(-50%); z-index: 1000;" data-aos="fade-left" data-aos-delay="500">
            <div class="d-flex flex-column gap-3">
                <a href="excel.php" class="btn btn-success rounded-circle shadow-lg" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;" data-bs-toggle="tooltip" data-bs-placement="right" title="تحميل ملف Excel">
                    <i class="fas fa-file-excel fa-lg"></i>
                </a>
                <button type="button" id="formreset" class="btn btn-primary rounded-circle shadow-lg" style="width: 60px; height: 60px;" data-bs-toggle="tooltip" data-bs-placement="right" title="افراغ الحقول">
                    <i class="fas fa-refresh fa-lg"></i>
                </button>
                <button type="button" class="btn btn-info rounded-circle shadow-lg" style="width: 60px; height: 60px;" data-bs-toggle="modal" data-bs-target="#FaceidModal" data-bs-toggle-tooltip data-bs-placement="right" title="بصمة وجه">
                    <i class="fas fa-user-check fa-lg"></i>
                </button>
                <button type="button" class="btn btn-warning FingerMatchBtn rounded-circle shadow-lg" style="width: 60px; height: 60px;" data-bs-toggle="modal" data-bs-target="#FingerMatchModal" data-bs-toggle-tooltip data-bs-placement="right" title="بصمة اصبع">
                    <i class="fas fa-fingerprint fa-lg"></i>
                </button>
            </div>
        </div>
        <!-- Main Container -->
        <div class="container-fluid px-4" style="max-width: 1400px; margin: 0 auto;">
            <!-- Enhanced Header -->
            <div class="header fade-in" data-aos="fade-down" data-aos-duration="800">
                <div class="row align-items-center">
                    <div class="col-lg-8 text-end">
                        <div class="header-content">
                            <h2 class="mb-2" style="color: var(--primary-color); font-weight: 700;">
                                <i class="fas fa-building me-2"></i>ديوان محافظة كركوك
                            </h2>
                            <div class="breadcrumb-custom mb-3">
                                <span class="breadcrumb-item">
                                    <i class="fas fa-users me-1"></i>قسم شؤون المواطنين
                                </span>
                                <span class="breadcrumb-separator mx-2">›</span>
                                <span class="breadcrumb-item">
                                    <i class="fas fa-user-tie me-1"></i>شعبة المختارين
                                </span>
                                <span class="breadcrumb-separator mx-2">›</span>
                                <span class="breadcrumb-item active">
                                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                                </span>
                            </div>
                            <h4 class="text-primary mb-3">
                                <i class="fas fa-id-card me-2"></i>نافذة التقديم على هوية المختار
                            </h4>
                            <div class="action-buttons">
                                <a href="logout.php" class="btn btn-outline-danger me-2" data-bs-toggle="tooltip" data-bs-placement="bottom" title="تسجيل خروج">
                                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل خروج
                                </a>
                                <a target="_blank" href="https://www.kik.gov.iq/form/identity-almkhtar" class="btn btn-outline-primary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="الانتقال الى الاستمارة">
                                    <i class="fas fa-external-link-alt me-1"></i>الاستمارة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 text-center">
                        <div class="logo-section" data-aos="zoom-in" data-aos-delay="200">
                            <a href="https://id.kik.gov.iq/dashboard.php">
                                <img src="../asset/img/logo.webp" alt="شعار ديوان محافظة كركوك" class="img-fluid" style="max-width: 200px; filter: drop-shadow(0 5px 15px rgba(0,0,0,0.1));">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Main Content -->
            <div class="content" data-aos="fade-up" data-aos-delay="400">
                <!-- Statistics Cards -->
                <div class="row mb-4" data-aos="fade-up" data-aos-delay="600">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card border-0 h-100">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                                <h5 class="card-title">قيد المراجعة</h5>
                                <h3 class="text-warning"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد المراجعة'") ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card border-0 h-100">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-cog fa-2x text-info"></i>
                                </div>
                                <h5 class="card-title">قيد الانجاز</h5>
                                <h3 class="text-info"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد الانجاز'") ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card border-0 h-100">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                                <h5 class="card-title">انجزت</h5>
                                <h3 class="text-success"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'انجزت'") ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card stats-card border-0 h-100">
                            <div class="card-body text-center">
                                <div class="stats-icon mb-3">
                                    <i class="fas fa-redo fa-2x text-primary"></i>
                                </div>
                                <h5 class="card-title">تجديد</h5>
                                <h3 class="text-primary"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'تجديد'") ?></h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Search Section -->
                <div class="advancedSearch" data-aos="fade-up" data-aos-delay="800">
                    <div class="spanSearch">
                        <i class="fas fa-search me-2"></i>البحث المتقدم
                    </div>
                    <form id="search_form">
                        <div class="row g-3 text-end">
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="label1" placeholder="رقم الاستمارة">
                                    <label for="label1"><i class="fas fa-hashtag me-2"></i>رقم الاستمارة</label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="label2" placeholder="الاسم الرباعي">
                                    <label for="label2"><i class="fas fa-user me-2"></i>الاسم الرباعي</label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="label3" placeholder="رقم الهاتف">
                                    <label for="label3"><i class="fas fa-phone me-2"></i>رقم الهاتف</label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <select id="label4" class="form-select">
                                        <option value="">اختر الاجراء...</option>
                                        <option value="قيد المراجعة">قيد المراجعة</option>
                                        <option value="قيد الانجاز">قيد الانجاز</option>
                                        <option value="انجزت">انجزت</option>
                                        <option value="تجديد">تجديد</option>
                                    </select>
                                    <label for="label4"><i class="fas fa-tasks me-2"></i>الاجراء</label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="label5" placeholder="رمز ورقم الهوية">
                                    <label for="label5"><i class="fas fa-id-card me-2"></i>رمز ورقم الهوية</label>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="label6" placeholder="رمز ورقم الختم">
                                    <label for="label6"><i class="fas fa-stamp me-2"></i>رمز ورقم الختم</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Data Table Section -->
                <div class="table-section mt-4" data-aos="fade-up" data-aos-delay="1000">
                    <div class="card border-0">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>بيانات الاستمارات
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive" id="dynamic_content">
                                <!-- Dynamic content will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        if ($_SESSION['LoginToAdminIdentityID'] != 18) {
        ?>
            <!-- Enhanced Modal Count Status -->
            <div class="modal fade" id="CountStatusModal" tabindex="-1" aria-labelledby="CountStatusModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title mx-auto" id="CountStatusModalLabel">
                                <i class="fas fa-chart-bar me-2"></i>احصائيات الاجراءات
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-3 text-center">
                                <div class="col-md-3 col-sm-6">
                                    <div class="stats-item p-3 rounded">
                                        <div class="stats-icon mb-2">
                                            <i class="fas fa-clock fa-2x text-warning"></i>
                                        </div>
                                        <h6 class="text-warning fw-bold">قيد المراجعة</h6>
                                        <h3 class="text-warning"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد المراجعة'") ?></h3>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="stats-item p-3 rounded">
                                        <div class="stats-icon mb-2">
                                            <i class="fas fa-cog fa-2x text-info"></i>
                                        </div>
                                        <h6 class="text-info fw-bold">قيد الانجاز</h6>
                                        <h3 class="text-info"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'قيد الانجاز'") ?></h3>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="stats-item p-3 rounded">
                                        <div class="stats-icon mb-2">
                                            <i class="fas fa-check-circle fa-2x text-success"></i>
                                        </div>
                                        <h6 class="text-success fw-bold">انجزت</h6>
                                        <h3 class="text-success"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'انجزت'") ?></h3>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="stats-item p-3 rounded">
                                        <div class="stats-icon mb-2">
                                            <i class="fas fa-redo fa-2x text-primary"></i>
                                        </div>
                                        <h6 class="text-primary fw-bold">تجديد</h6>
                                        <h3 class="text-primary"><?php echo $db->RowCountData("SELECT status FROM identityalmkhtar WHERE status = 'تجديد'") ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- View Form Modal -->
            <div class="modal fade" id="viewFormModalLong" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-right">
                            <ul class="list-group">
                                <li id="msgCopy" class="list-group-item alert alert-success" style="display:none;">تم النسخ
                                </li>
                                <li class="list-group-item text-center f0"><img width="170" src=""></li>
                                <li class="list-group-item f1"></li>
                                <li class="list-group-item f2"></li>
                                <li class="list-group-item f3" id="copyf3"></li>
                                <li class="list-group-item f8" id="copyf8"></li>
                                <li class="list-group-item f888" id="copyf888"></li>
                                <li class="list-group-item f9" id="copyf9"></li>
                                <li class="list-group-item f10" id="copyf10"></li>
                                <li class="list-group-item f5"></li>
                                <li class="list-group-item f7"></li>
                                <li class="list-group-item f6"></li>
                                <li class="list-group-item f4"></li>
                                <li class="list-group-item f11"></li>
                                <li class="list-group-item f12"></li>
                                <li class="list-group-item qrcode text-center">
                                    <img src=''>
                                </li>
                                <li class="list-group-item fingerprint text-center">
                                    <img width="100" src=''>
                                </li>
                                <li class="list-group-item f13 text-center">
                                    <iframe src="" style="display:none;" id="printframe" name="frame"></iframe>
                                    <button type="button" class="btn btn-primary" onclick="frames['frame'].print()">طباعة</button>
                                </li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End View Form Modal -->
            <!-- Enhanced Edit Form Modal -->
            <div class="modal fade" id="editFormModalLong" tabindex="-1" aria-labelledby="fullNameHeaderModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="fullNameHeaderModalLabel">
                                <i class="fas fa-edit me-2"></i>تعديل الاستمارة
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form method="POST" id="formSubmit" enctype="multipart/form-data">
                            <input type="hidden" id="getid" name="getid">
                            <div class="modal-body text-end">
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" readonly id="Input0" placeholder="رقم الاستمارة">
                                            <label for="Input0"><i class="fas fa-hashtag me-2"></i>رقم الاستمارة</label>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="Input1" name="fullname" placeholder="الاسم الرباعي" required>
                                            <label for="Input1"><i class="fas fa-user me-2"></i>الاسم الرباعي</label>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-map-marker-alt me-2"></i>محل وتاريخ الولادة
                                        </label>
                                        <div class="row g-2">
                                            <div class="col-md-4">
                                                <div class="form-floating">
                                                    <select class="form-select" name="goverment" id="Input2" required>
                                                        <option value="">اختر المحافظة</option>
                                                        <option value="كركوك">كركوك</option>
                                                        <option value="بغداد">بغداد</option>
                                                        <option value="البصرة">البصرة</option>
                                                        <option value="ميسان">ميسان</option>
                                                        <option value="ذي قار">ذي قار</option>
                                                        <option value="الديوانية">الديوانية</option>
                                                        <option value="المثنى">المثنى</option>
                                                        <option value="النجف الاشرف">النجف الاشرف</option>
                                                        <option value="كربلاء المقدسة">كربلاء المقدسة</option>
                                                        <option value="بابل">بابل</option>
                                                        <option value="واسط">واسط</option>
                                                        <option value="ديالى">ديالى</option>
                                                        <option value="صلاح الدين">صلاح الدين</option>
                                                        <option value="نينوى">نينوى</option>
                                                        <option value="الانبار">الانبار</option>
                                                        <option value="اربيل">اربيل</option>
                                                        <option value="دهوك">دهوك</option>
                                                        <option value="سليمانية">سليمانية</option>
                                                    </select>
                                                    <label for="Input2">المحافظة</label>
                                                </div>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="form-floating">
                                                    <input type="date" class="form-control" name="databirth" id="Input22" required>
                                                    <label for="Input22">تاريخ الولادة</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="bloadtype" id="Input3" placeholder="فصيلة الدم">
                                            <label for="Input3"><i class="fas fa-tint me-2"></i>فصيلة الدم</label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="tel" class="form-control" name="phonenumber" id="Input4" placeholder="رقم الهاتف" pattern="[0-9]{11}">
                                            <label for="Input4"><i class="fas fa-phone me-2"></i>رقم الهاتف</label>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="elimination" id="Input5" placeholder="القضاء">
                                            <label for="Input5"><i class="fas fa-map me-2"></i>القضاء</label>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="side" id="Input55" placeholder="الناحية">
                                            <label for="Input55"><i class="fas fa-map-pin me-2"></i>الناحية</label>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="village" id="Input555" placeholder="القرية او المحلة">
                                            <label for="Input555"><i class="fas fa-home me-2"></i>القرية او المحلة</label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="numberidentity" id="Input6" placeholder="رمز ورقم الهوية">
                                            <label for="Input6"><i class="fas fa-id-card me-2"></i>رمز ورقم الهوية</label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" name="numberstamp" id="Input7" placeholder="رمز ورقم الختم">
                                            <label for="Input7"><i class="fas fa-stamp me-2"></i>رمز ورقم الختم</label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-camera me-2"></i>الصورة الشخصية
                                        </label>
                                        <div class="file-upload">
                                            <div class="image-upload-wrap" style="display: none;">
                                                <input class="file-upload-input" type='file' name="photo" onchange="readURL(this);" accept="image/*" />
                                                <input type="hidden" name="oldphoto" id="oldphoto">
                                                <div class="drag-text">
                                                    <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-primary"></i>
                                                    <h5>اسحب او اضغط لرفع صورة شخصية</h5>
                                                    <small class="text-muted">
                                                        يجب ان يكون حجم الصورة كحد اقصى 500 كيلوبايت
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="file-upload-content" style="display: block;">
                                                <img class="file-upload-image rounded" src="" alt="الصورة الشخصية" />
                                                <div class="image-title-wrap mt-3">
                                                    <button type="button" onclick="removeUpload()" class="btn btn-outline-danger btn-sm">
                                                        <i class="fas fa-trash me-1"></i>حذف الصورة
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i>إغلاق
                                </button>
                                <button type="submit" id="btnSubmit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>تحديث البيانات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Edit Form Modal -->
            <!-- Change Status Modal -->
            <div class="modal fade" id="ChangeStatusModalCenter" tabindex="-1" role="dialog" aria-labelledby="ChangeStatusModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="ChangeStatusModalCenterTitle">تغير حالة الاستمارة</h5>
                        </div>
                        <h6 id="getfullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <form method="POST" id="ChangeStatusForm">
                            <input type="hidden" name="getid" id="getidstatus">
                            <div class="modal-body text-center">
                                <select id="statusSelect" class="form-control" name="statusSelect" style="padding: 0!important;">
                                    <option value="قيد المراجعة">
                                        قيد المراجعة
                                    </option>
                                    <option value="قيد الانجاز">
                                        قيد الانجاز
                                    </option>
                                    <option value="انجزت">
                                        انجزت
                                    </option>
                                    <option value="تجديد">
                                        تجديد
                                    </option>
                                </select>
                            </div>
                            <div class="modal-footer">
                                <input type="submit" class="btn btn-outline-success m-auto" id="btnStatus" value="تحديث">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Change Status Modal -->
            <!-- Download Upload Pdf Modal -->
            <div class="modal fade" id="DownloadUploadPdfModalCenter" tabindex="-1" role="dialog" aria-labelledby="DownloadUploadPdfModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="DownloadUploadPdfModalCenterTitle">رفع او تحميل PDF</h5>
                        </div>
                        <h6 id="getpdffullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <div class="text-center" id="pdfIcon">

                        </div>
                        <div class="m-auto pt-3">
                            <button class="btn btn-danger" id="deletepdf" title="مسح"><i class="fas fa-trash"></i></button>
                        </div>
                        <form method="POST" id="DownloadUploadPdf">
                            <input type="hidden" name="getpdfid" id="getpdfid">
                            <div class="modal-body text-center">
                                <input type="file" name="file" id="file" class="form-control">
                            </div>
                            <div class="modal-footer">
                                <input type="submit" class="btn btn-outline-success m-auto" id="btnDownloadPdf" value="تحديث">
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- End Download Upload Pdf Modal -->
            <!-- Download Upload Corel Modal -->
            <div class="modal fade" id="DownloadUploadCorelModalCenter" tabindex="-1" role="dialog" aria-labelledby="DownloadUploadPdfModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="DownloadUploadCorelModalCenterTitle">تحميل Corel</h5>
                        </div>
                        <h6 id="getcorelfullname" style="text-align: center;margin-top: 18px;font-weight: bold;"></h6>
                        <hr>
                        <div class="text-center" id="corelIcon">

                        </div>
                        <div class="m-auto pt-3 pb-3">
                            <button class="btn btn-danger" id="deletecorel" title="مسح"><i class="fas fa-trash"></i></button>
                        </div>
                        <?php
                        if ($_SESSION['LoginToAdminIdentity'] == 'w.site') {
                        ?>
                            <div class="modal-footer">
                                <a target="_blank" class="btn btn-outline-success m-auto mt-4" id="btnDownloadCorel">رفع</a>
                                <button type="button" class="btn btn-outline-secondary m-auto" data-dismiss="modal">اغلاق</button>
                            </div>
                        <?php
                        } else { ?>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-outline-secondary m-auto" data-dismiss="modal">اغلاق</button>
                            </div>
                        <?php }
                        ?>
                    </div>
                </div>
            </div>
            <!-- Take Face Id -->
            <div class="modal fade" id="TakeFaceId" tabindex="-1" role="dialog" aria-labelledby="TakeFaceIdTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="TakeFaceIdTitle">بصمة الوجه</h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="d-none faceidinfo">
                                <span class="form_id"></span>
                                <span class="form_fullname"></span>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <video id="webcam" autoplay playsinline width="300"></video>
                                    <canvas id="canvas" class="d-none"></canvas>
                                </div>
                                <div class="col-6">
                                    <img src="" id="download-photo" alt="" width="300">
                                </div>
                            </div>
                            <div class="mt-3">
                                <img src="" class="d-none" id="fetchImg">
                                <button type="button" class="btn btn-secondary fetchpic">جلب الصورة الشخصية</button>
                                <button type="button" class="btn btn-primary takepic">اخذ لقطة</button>
                                <button type="submit" class="btn btn-info uploadface">رفع</button>
                            </div>
                            <div class="msgAlert">

                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>
            <!-- View Face Id -->
            <div class="modal fade" id="FaceidModal" tabindex="-1" role="dialog" aria-labelledby="FaceidModalTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="FaceidModalTitle">بصمة الوجه</h5>
                        </div>
                        <div class="modal-body text-center position-relative" id="appendCanvas">
                            <video id="video" width="600" height="450" autoplay>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>

            <!-- View Finger Scanner -->
            <div class="modal fade" id="FingerMatchModal" tabindex="-1" role="dialog" aria-labelledby="FingerMatchModalTitle" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="FingerMatchModalTitle">بصمة الاصبع</h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="messageStatusFinger text-center">

                            </div>
                            <div id="appenddata" style="padding: 32px;">

                            </div>
                            <div class="text-center stepfinger2">

                            </div>
                            <button type="button" class="btn btn-outline-primary" id="gettem">تحقق</button>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary m-auto" id="closemodeltake" data-dismiss="modal">اغلاق</button>
                        </div>

                    </div>
                </div>
            </div>
        <?php
        } else { ?>
            <!-- View Form Modal -->
            <div class="modal fade" id="viewFormModalLong" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-body text-right">
                            <ul class="list-group">
                                <li id="msgCopy" class="list-group-item alert alert-success" style="display:none;">تم النسخ
                                </li>
                                <li class="list-group-item text-center f0"><img width="170" src=""></li>
                                <li class="list-group-item f1"></li>
                                <li class="list-group-item f2"></li>
                                <li class="list-group-item f3" id="copyf3"></li>
                                <li class="list-group-item f8" id="copyf8"></li>
                                <li class="list-group-item f888" id="copyf888"></li>
                                <li class="list-group-item f9" id="copyf9"></li>
                                <li class="list-group-item f10" id="copyf10"></li>
                                <li class="list-group-item f5"></li>
                                <li class="list-group-item f7"></li>
                                <li class="list-group-item f6"></li>
                                <li class="list-group-item f4"></li>
                                <li class="list-group-item f11"></li>
                                <li class="list-group-item f12"></li>
                                <li class="list-group-item qrcode text-center">
                                    <img src=''>
                                </li>
                                <li class="list-group-item fingerprint text-center">
                                    <img width="100" src=''>
                                </li>
                                <li class="list-group-item f13 text-center">
                                    <iframe src="" style="display:none;" id="printframe" name="frame"></iframe>
                                    <button type="button" class="btn btn-primary" onclick="frames['frame'].print()">طباعة</button>
                                </li>
                            </ul>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        <?php
        }
        ?>
        <!-- End Download Upload Corel Modal -->
        <!-- Scripts -->
        <!-- jQuery 3.6 -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <!-- Bootstrap 5.3 JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <!-- AOS Animation -->
        <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <!-- Webcam Easy -->
        <script type="text/javascript" src="https://unpkg.com/webcam-easy/dist/webcam-easy.min.js"></script>
        <!-- Font Awesome -->
        <script src="all.min.js"></script>
        <!-- Face API -->
        <script src="face-api.min.js"></script>
        <!-- Custom JS -->
        <script src="style.js"></script>

        <script>
            // Initialize AOS
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Hide loading screen after page load
            $(window).on('load', function() {
                setTimeout(function() {
                    $('#loadingScreen').fadeOut(500);
                }, 1000);
            });

            // Enhanced form reset with animation
            $('#formreset').on('click', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'تأكيد العملية',
                    text: 'هل تريد افراغ جميع حقول البحث؟',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3498db',
                    cancelButtonColor: '#e74c3c',
                    confirmButtonText: 'نعم، افراغ الحقول',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $('#search_form')[0].reset();
                        $('.form-floating input, .form-floating select').each(function() {
                            $(this).removeClass('is-valid is-invalid');
                        });
                        load_data(1);

                        Swal.fire({
                            title: 'تم بنجاح!',
                            text: 'تم افراغ جميع حقول البحث',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    }
                });
            });

            // Add floating animation to stats cards
            $('.stats-card').hover(
                function() {
                    $(this).addClass('shadow-lg').css('transform', 'translateY(-10px)');
                },
                function() {
                    $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
                }
            );

            // Add smooth scrolling for internal links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Enhanced search form validation
            $('#search_form input, #search_form select').on('input change', function() {
                const value = $(this).val();
                if (value && value.length > 0) {
                    $(this).addClass('is-valid').removeClass('is-invalid');
                } else {
                    $(this).removeClass('is-valid is-invalid');
                }
            });
        </script>
        <?php
        if ($_SESSION['LoginToAdminIdentityID'] != 18) {

            $gfg_folderpath = 'uploads/face/';
            $arrfile = array();
            // CHECKING WHETHER PATH IS A DIRECTORY OR NOT
            if (is_dir($gfg_folderpath)) {
                // GETTING INTO DIRECTORY
                $files = opendir($gfg_folderpath); {
                    // CHECKING FOR SMOOTH OPENING OF DIRECTORY
                    if ($files) {
                        //READING NAMES OF EACH ELEMENT INSIDE THE DIRECTORY
                        while (($gfg_subfolder = readdir($files)) !== FALSE) {
                            // CHECKING FOR FILENAME ERRORS
                            if ($gfg_subfolder != '.' && $gfg_subfolder != '..') {
                                array_push($arrfile, $gfg_subfolder);
                            }
                        }
                    }
                }
            }

        ?>
            <script>
                var folderlist = [];
                <?php
                foreach ($arrfile as $key => $value) {

                ?>
                    folderlist.push("<?php echo $value; ?>")
                <?php
                }

                ?>

                $("#FaceidModal").on('show.bs.modal', function() {
                    const video = document.getElementById('video');

                    Promise.all([
                        faceapi.nets.ssdMobilenetv1.loadFromUri('models'),
                        faceapi.nets.faceRecognitionNet.loadFromUri('models'),
                        faceapi.nets.faceLandmark68Net.loadFromUri('models'),
                    ]).then(startWebcam);

                    function startWebcam() {
                        navigator.mediaDevices
                            .getUserMedia({
                                video: true,
                                audio: false,
                            })
                            .then(stream => {
                                video.srcObject = stream;
                            })
                            .catch(error => {
                                console.error(error);
                            });
                    }

                    function getLabeledFaceDescriptions() {
                        const labels = folderlist;
                        return Promise.all(
                            labels.map(async label => {
                                const descriptions = [];
                                // for (let i = 1; i <= 2; i++) {
                                const img = await faceapi.fetchImage(`uploads/face/${label}/1.png`);
                                const detections = await faceapi
                                    .detectSingleFace(img)
                                    .withFaceLandmarks()
                                    .withFaceDescriptor();
                                if (detections != undefined && detections != null) {
                                    console.log(detections);
                                    descriptions.push(detections.descriptor);
                                }
                                // }
                                return new faceapi.LabeledFaceDescriptors(label, descriptions);
                            })
                        );
                    }
                    video.addEventListener('play', async () => {
                        const labeledFaceDescriptors = await getLabeledFaceDescriptions();
                        const faceMatcher = new faceapi.FaceMatcher(labeledFaceDescriptors);

                        const canvas = faceapi.createCanvasFromMedia(video);
                        document.getElementById("appendCanvas").append(canvas);

                        const displaySize = {
                            width: video.width,
                            height: video.height
                        };
                        faceapi.matchDimensions(canvas, displaySize);

                        setInterval(async () => {
                            const detections = await faceapi
                                .detectAllFaces(video)
                                .withFaceLandmarks()
                                .withFaceDescriptors();

                            const resizedDetections = faceapi.resizeResults(detections, displaySize);

                            canvas.getContext('2d', {
                                willReadFrequently: true
                            }).clearRect(0, 0, canvas.width, canvas.height);

                            const results = resizedDetections.map(d => {
                                return faceMatcher.findBestMatch(d.descriptor);
                            });
                            results.forEach((result, i) => {
                                const box = resizedDetections[i].detection.box;
                                const drawBox = new faceapi.draw.DrawBox(box, {
                                    label: result,
                                });
                                drawBox.draw(canvas);
                            });
                        }, 100);
                    });
                });
                $("#FaceidModal").on('hide.bs.modal', function() {
                    location.reload();
                });
                // Start Capture WebCam
                const webcamElement = document.getElementById('webcam');
                const canvasElement = document.getElementById('canvas');
                const webcam = new Webcam(webcamElement, 'user', canvasElement);

                $(document).on('click', '.TakeFaceId', function() {
                    webcam.start()
                        .then(result => {
                            console.log("webcam started");
                        })
                        .catch(err => {
                            console.log(err);
                        });
                    $('#TakeFaceId').modal('show');
                    $('#fetchImg').attr('src', 'https://kik.gov.iq/public/storage/' + $(this).attr('data-image'));
                    $('#TakeFaceIdTitle').text($(this).attr('data-fullname'));

                    var pathImg = 'uploads/face/' + $(this).attr('data-fullname') + '/1.png';

                    if (pathImg != "") {
                        $.ajax({
                            url: pathImg,
                            type: 'HEAD',
                            error: function() {
                                $('#download-photo').attr('src', 'uploads/No-Image-Placeholder.svg.png');
                            },
                            success: function() {
                                $('#download-photo').attr('src', pathImg);
                            }
                        });

                    }

                    $(".faceidinfo .form_id").text($(this).attr('id'));
                    $(".faceidinfo .form_fullname").text($(this).attr('data-fullname'));
                });
                $(".takepic").on('click', function() {
                    let picture = webcam.snap();
                    $("#download-photo").attr('src', picture);
                });

                $("#TakeFaceId").on('hide.bs.modal', function() {
                    webcam.stop();
                    $('#download-photo').attr('src', '');
                });
                $('.uploadface').on('click', function() {
                    var id = $(".faceidinfo .form_id").text(),
                        datafullname = $(".faceidinfo .form_fullname").text(),
                        photo = $("#download-photo").attr('src');
                    if ($('#download-photo').attr('src') != 'uploads/No-Image-Placeholder.svg.png') {
                        $.ajax({
                            url: 'upload_face.php',
                            method: 'POST',
                            data: {
                                id: id,
                                datafullname: datafullname,
                                photo: photo,
                            },
                            success: function(data) {
                                $(".msgAlert").html('<div class="alert alert-success mt-3" role="alert">تم حفظ بصمة الوجه بنجاح</div>');
                                setTimeout(() => {
                                    $(".msgAlert").html('');
                                }, 2000);
                            }
                        });
                    } else {
                        $(".msgAlert").html('<div class="alert alert-danger mt-3" role="alert">يرجى اخذ بصمة وجه!</div>');
                        setTimeout(() => {
                            $(".msgAlert").html('');
                        }, 2000);
                    }
                });
            </script>
            <script src="finger.js"></script>
        <?php
        }
        ?>
    </body>

    </html>
<?php
} else {
    header('Location:index.php');
    exit();
}
ob_end_flush();
