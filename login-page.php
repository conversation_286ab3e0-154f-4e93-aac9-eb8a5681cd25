<?php
session_start();
require '../init.php';

// Check if user is already logged in
if (isset($_SESSION['LoginToAdminIdentityAlmkhtar'])) {
    header('Location: dashboard.php');
    exit();
}

// Check if email coming from HTTP post request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = trim($_POST['InputEmail']);
    $pass = $_POST['InputPassword'];
    
    // Check if the email exist in database
    $id = $db->Fetch("SELECT ID, Email, Password FROM users WHERE Email = ? AND Rules = 12 LIMIT 1", [$email]);
    if ($id == true && password_verify($pass, $id['Password'])) {
        $_SESSION['LoginToAdminIdentityAlmkhtar'] = $email; // Register Session Name 
        $_SESSION['LoginToAdminIdentityAlmkhtarID'] = $id['ID']; // Register Session ID
        echo json_encode(['status' => 'success', 'message' => 'تم تسجيل الدخول بنجاح']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'البريد الإلكتروني أو كلمة المرور غير صحيحة']);
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة هوية المختار</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-radius: 15px;
            --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('bg.jpg') center/cover;
            opacity: 0.1;
            z-index: -1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo-section img {
            max-width: 120px;
            height: auto;
            margin-bottom: 1rem;
            filter: drop-shadow(0 5px 15px rgba(0,0,0,0.1));
        }

        .logo-section h2 {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .logo-section p {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            font-size: 1rem;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.8);
        }

        .form-floating > .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            background: rgba(255, 255, 255, 1);
        }

        .form-floating > label {
            color: var(--text-light);
            font-weight: 500;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .footer-text {
            text-align: center;
            margin-top: 2rem;
            color: var(--text-light);
            font-size: 0.85rem;
        }

        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .logo-section h2 {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container" data-aos="fade-up" data-aos-duration="800">
        <div class="logo-section" data-aos="fade-down" data-aos-delay="200">
            <img src="logo.gif" alt="شعار ديوان محافظة كركوك" class="img-fluid">
            <h2>نظام إدارة هوية المختار</h2>
            <p>ديوان محافظة كركوك - قسم شؤون المواطنين</p>
        </div>

        <form id="loginForm" data-aos="fade-up" data-aos-delay="400">
            <div class="form-floating">
                <input type="email" class="form-control" id="InputEmail" name="InputEmail" placeholder="البريد الإلكتروني" required>
                <label for="InputEmail"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني</label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="InputPassword" name="InputPassword" placeholder="كلمة المرور" required>
                <label for="InputPassword"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
            </div>

            <button type="submit" class="btn btn-login" id="loginBtn">
                <span class="loading-spinner" id="loadingSpinner"></span>
                <span id="btnText"><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</span>
            </button>
        </form>

        <div class="footer-text" data-aos="fade-up" data-aos-delay="600">
            <p>&copy; 2024 ديوان محافظة كركوك - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- Scripts -->
    <!-- jQuery 3.6 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Login form handling
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            
            const email = $('#InputEmail').val();
            const password = $('#InputPassword').val();
            
            if (!email || !password) {
                Swal.fire({
                    icon: 'warning',
                    title: 'تنبيه',
                    text: 'يرجى ملء جميع الحقول المطلوبة',
                    confirmButtonText: 'موافق',
                    confirmButtonColor: '#3498db'
                });
                return;
            }

            // Show loading state
            $('#loadingSpinner').show();
            $('#btnText').html('<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...');
            $('#loginBtn').prop('disabled', true);

            $.ajax({
                url: 'login-page.php',
                method: 'POST',
                data: {
                    InputEmail: email,
                    InputPassword: password
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'نجح تسجيل الدخول',
                            text: response.message,
                            confirmButtonText: 'متابعة',
                            confirmButtonColor: '#27ae60'
                        }).then(() => {
                            window.location.href = 'dashboard.php';
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ في تسجيل الدخول',
                            text: response.message,
                            confirmButtonText: 'موافق',
                            confirmButtonColor: '#e74c3c'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في الاتصال',
                        text: 'حدث خطأ في الاتصال بالخادم، يرجى المحاولة مرة أخرى',
                        confirmButtonText: 'موافق',
                        confirmButtonColor: '#e74c3c'
                    });
                },
                complete: function() {
                    // Hide loading state
                    $('#loadingSpinner').hide();
                    $('#btnText').html('<i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول');
                    $('#loginBtn').prop('disabled', false);
                }
            });
        });

        // Add floating animation to form elements
        $('.form-control').on('focus', function() {
            $(this).parent().addClass('focused');
        }).on('blur', function() {
            if (!$(this).val()) {
                $(this).parent().removeClass('focused');
            }
        });
    </script>
</body>
</html>
