$(function () {
  'use strict';

  // Initialize Bootstrap 5 tooltips
  var tooltipTriggerList = [].slice.call(
    document.querySelectorAll('[data-bs-toggle="tooltip"]')
  );
  var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Enhanced form reset with SweetAlert2
  $('#formreset').on('click', function (e) {
    e.preventDefault();

    Swal.fire({
      title: 'تأكيد العملية',
      text: 'هل تريد افراغ جميع حقول البحث؟',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3498db',
      cancelButtonColor: '#e74c3c',
      confirmButtonText: 'نعم، افراغ الحقول',
      cancelButtonText: 'إلغاء',
      reverseButtons: true,
    }).then(result => {
      if (result.isConfirmed) {
        $('#search_form').trigger('reset');
        $('.form-floating input, .form-floating select').each(function () {
          $(this).removeClass('is-valid is-invalid');
        });
        load_data(1);

        // Show success message
        Swal.fire({
          title: 'تم بنجاح!',
          text: 'تم افراغ جميع حقول البحث',
          icon: 'success',
          timer: 1500,
          showConfirmButton: false,
          toast: true,
          position: 'top-end',
        });
      }
    });
  });

  // Load initial data with loading animation
  showLoadingSpinner();
  load_data(1);

  // Enhanced load_data function with loading states and error handling
  function load_data(
    page,
    query = '',
    query2 = '',
    query3 = '',
    query4 = '',
    query5 = '',
    query6 = ''
  ) {
    // Show loading state
    showLoadingSpinner();

    $.ajax({
      url: 'fetch.php',
      method: 'POST',
      data: {
        page: page,
        query: query,
        query2: query2,
        query3: query3,
        query4: query4,
        query5: query5,
        query6: query6,
      },
      beforeSend: function () {
        // Add loading class to search form
        $('#search_form').addClass('loading');
      },
      success: function (data) {
        hideLoadingSpinner();
        $('#dynamic_content').html(data);

        // Add fade-in animation to new content
        $('#dynamic_content').hide().fadeIn(500);

        // Initialize tooltips for new content
        var newTooltips = [].slice.call(
          document.querySelectorAll(
            '#dynamic_content [data-bs-toggle="tooltip"]'
          )
        );
        newTooltips.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl);
        });
      },
      error: function (xhr, status, error) {
        hideLoadingSpinner();
        console.error('Error loading data:', error);

        Swal.fire({
          title: 'خطأ في تحميل البيانات',
          text: 'حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.',
          icon: 'error',
          confirmButtonText: 'موافق',
          confirmButtonColor: '#e74c3c',
        });
      },
      complete: function () {
        // Remove loading class from search form
        $('#search_form').removeClass('loading');
      },
    });
  }

  // Loading spinner functions
  function showLoadingSpinner() {
    if ($('#dynamic_content .spinner').length === 0) {
      $('#dynamic_content').html(
        '<div class="text-center py-5"><div class="spinner"></div><p class="mt-3 text-muted">جاري تحميل البيانات...</p></div>'
      );
    }
  }

  function hideLoadingSpinner() {
    $('#dynamic_content .spinner').parent().remove();
  }

  $(document).on('click', '.page-link', function () {
    var page = $(this).data('page_number'),
      query = $('#label1').val(),
      query2 = $('#label2').val(),
      query3 = $('#label3').val(),
      query4 = $('#label4').val(),
      query5 = $('#label5').val(),
      query6 = $('#label6').val();
    load_data(page, query, query2, query3, query4, query5, query6);
  });

  // Enhanced search with debouncing and validation
  let searchTimeout;
  $('#search_form').on('keyup change', function () {
    clearTimeout(searchTimeout);

    // Add visual feedback
    const $form = $(this);
    $form.addClass('searching');

    searchTimeout = setTimeout(function () {
      var query = $('#label1').val(),
        query2 = $('#label2').val(),
        query3 = $('#label3').val(),
        query4 = $('#label4').val(),
        query5 = $('#label5').val(),
        query6 = $('#label6').val();

      // Validate inputs
      validateSearchInputs();

      load_data(1, query, query2, query3, query4, query5, query6);
      $form.removeClass('searching');
    }, 500); // Debounce for 500ms
  });

  // Input validation function
  function validateSearchInputs() {
    $('#search_form input, #search_form select').each(function () {
      const $input = $(this);
      const value = $input.val();

      if (value && value.length > 0) {
        $input.addClass('is-valid').removeClass('is-invalid');
      } else {
        $input.removeClass('is-valid is-invalid');
      }
    });
  }

  // Add real-time validation
  $('#search_form input, #search_form select').on('input change', function () {
    const $input = $(this);
    const value = $input.val();

    if (value && value.length > 0) {
      $input.addClass('is-valid').removeClass('is-invalid');
    } else {
      $input.removeClass('is-valid is-invalid');
    }
  });
  // Enhanced fetchpic with better UX
  $(document).on('click', '.fetchpic', function () {
    const $btn = $(this);
    const originalText = $btn.html();

    $.ajax({
      type: 'POST',
      url: 'imgtobase64.php',
      data: { img: $('#fetchImg').attr('src') },
      beforeSend: function () {
        $btn.prop('disabled', true);
        $btn.html(
          '<i class="fas fa-spinner fa-spin me-2"></i>جاري جلب الصورة...'
        );
      },
      success: function (data) {
        $('#download-photo').attr('src', data);
        $btn.html('<i class="fas fa-check me-2"></i>تم جلب الصورة');

        // Show success toast
        Swal.fire({
          title: 'تم بنجاح!',
          text: 'تم جلب الصورة الشخصية',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false,
          toast: true,
          position: 'top-end',
        });

        setTimeout(() => {
          $btn.html(originalText);
        }, 2000);
      },
      error: function () {
        Swal.fire({
          title: 'خطأ!',
          text: 'فشل في جلب الصورة الشخصية',
          icon: 'error',
          confirmButtonText: 'موافق',
        });

        $btn.html(originalText);
      },
      complete: function () {
        $btn.prop('disabled', false);
      },
    });
  });

  // Add smooth animations for interactive elements
  $(document)
    .on('mouseenter', '.btn', function () {
      $(this).addClass('shadow-sm');
    })
    .on('mouseleave', '.btn', function () {
      $(this).removeClass('shadow-sm');
    });

  // Enhanced table row interactions
  $(document)
    .on('mouseenter', '.table tbody tr', function () {
      $(this).addClass('table-hover-effect');
    })
    .on('mouseleave', '.table tbody tr', function () {
      $(this).removeClass('table-hover-effect');
    });

  // Add loading state to forms
  $(document).on('submit', 'form', function () {
    const $form = $(this);
    const $submitBtn = $form.find(
      'button[type="submit"], input[type="submit"]'
    );

    $submitBtn.prop('disabled', true);
    $submitBtn.html(
      '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...'
    );

    setTimeout(() => {
      $submitBtn.prop('disabled', false);
      $submitBtn.html($submitBtn.data('original-text') || 'إرسال');
    }, 3000);
  });

  // Initialize AOS animations if available
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100,
    });
  }

  // Add keyboard shortcuts
  $(document).on('keydown', function (e) {
    // Ctrl + R for reset form
    if (e.ctrlKey && e.keyCode === 82) {
      e.preventDefault();
      $('#formreset').click();
    }

    // Escape to close modals
    if (e.keyCode === 27) {
      $('.modal.show').modal('hide');
    }
  });

  // Add auto-save functionality for forms (optional)
  let autoSaveTimeout;
  $('#search_form input, #search_form select').on('input change', function () {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
      // Save form data to localStorage
      const formData = $('#search_form').serialize();
      localStorage.setItem('searchFormData', formData);
    }, 1000);
  });

  // Restore form data on page load
  const savedFormData = localStorage.getItem('searchFormData');
  if (savedFormData) {
    const params = new URLSearchParams(savedFormData);
    params.forEach((value, key) => {
      $(`#${key}`).val(value);
    });
  }
});
function readURL(input) {
  'use strict';
  if (input.files && input.files[0]) {
    var reader = new FileReader();

    reader.onload = function (e) {
      $('.image-upload-wrap').hide();

      $('.file-upload-image').attr('src', e.target.result);
      $('.file-upload-content').show();

      $('.image-title').html(input.files[0].name);
    };

    reader.readAsDataURL(input.files[0]);
  } else {
    removeUpload();
  }
}

function removeUpload() {
  'use strict';
  let confirmRemove = confirm('هل انت متأكد؟');
  if (confirmRemove == true) {
    $('.file-upload-input').replaceWith($('.file-upload-input').clone());
    $('.file-upload-content').hide();
    $('.image-upload-wrap').show();
  }
}
$('.image-upload-wrap').bind('dragover', function () {
  $('.image-upload-wrap').addClass('image-dropping');
});
$('.image-upload-wrap').bind('dragleave', function () {
  $('.image-upload-wrap').removeClass('image-dropping');
});

function showMyImage(fileInput) {
  'use strict';
  document.getElementById('thumbnil').style.display = '';
  document.getElementById('showbotton').style.display = '';
  var files = fileInput.files;
  for (var i = 0; i < files.length; i++) {
    var file = files[i];
    var imageType = /image.*/;
    if (!file.type.match(imageType)) {
      continue;
    }
    var img = document.getElementById('thumbnil');
    img.file = file;
    var reader = new FileReader();
    reader.onload = (function (aImg) {
      return function (e) {
        aImg.src = e.target.result;
      };
    })(img);
    reader.readAsDataURL(file);
  }
}
